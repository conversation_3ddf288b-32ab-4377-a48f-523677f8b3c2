import contextlib
import os
import time
from datetime import UTC, datetime
from os import environ
from urllib.parse import parse_qs

import requests
from boto3.dynamodb.conditions import Attr, Key

from utils.aws_utils import get_dynamodb_table, get_secret, get_secret_string
from utils.dict_utils import capitalizeKeys, first, get
from utils.errors import BadRequestError, NotFoundError
from utils.log_utils import log_info_json, log_warning_json

mollie_table = get_dynamodb_table(f"MollieStatus_{os.environ['STAGE_TAG']}")


# Not as useless as you think, used so I can mock the TTL in testing
def create_ttl_year():
    current_time = int(time.time())

    # TTL = Today + 365 days
    return current_time + (365 * 24 * 60 * 60)  # 365days in seconds


def update(event, config):
    payment_id = next(iter(parse_qs(event.get("body") or "").get("id")))

    # Id is mandatory
    if not payment_id:
        log_warning_json({"error": "<PERSON>llie callback without ID", "request": event})
        raise BadRequestError("L'id est obligatoire dans la requête")

    resp = requests.get(
        url=f"https://api.mollie.com/v2/payments/{payment_id}",
        headers={"Authorization": f"Bearer {get_secret_string(environ['MOLLIE_TOKEN'])}"},
        timeout=10,
    )
    resp_data = resp.json()

    dossier_id = (resp_data.get("metadata") or {}).get("dossier")
    payment_type = (resp_data.get("metadata") or {}).get("type")
    status = resp_data.get("status")

    # OrderId and Status are mandatory
    if not dossier_id or not status or not isinstance(status, str) or not payment_type:
        log_warning_json(
            {
                "error": "Mollie Invalid DossierId, Status, or Type",
                "request": event,
                "response": resp,
                "response_data": resp_data,
            },
        )
        raise BadRequestError("Le dossier, status, ou type n'est pas présent ou invalide")
    else:
        log_info_json({"mollie_status": resp.status_code, "mollie_reponse": resp_data})

    # Send paid status to SAP
    if status == "paid":
        credentials = get_secret(os.environ["BASICAUTH"])
        resp_ws109 = requests.get(
            f"https://{os.environ['SAP']}/RESTAdapter/OS_APIRESA_RAC_WS109",
            auth=(credentials["user"], credentials["password"]),
            params={
                "Langue": "FR",
                "NumDossier": dossier_id,
                "TypeAction": f"{payment_type.upper()}_PAYE",
                "DateAccord": datetime.now().strftime("%d/%m/%Y"),
            },
            timeout=20,
            verify=False,
        )
        try:
            ws109_response = resp_ws109.json()
        except requests.exceptions.JSONDecodeError:
            ws109_response = resp_ws109.text
        log_info_json({"WS109_status": resp_ws109.status_code, "WS109_reponse": ws109_response})

    mollie_data = {
        "payment_id": payment_id,
        "order_id": dossier_id,
        "payment_type": payment_type.upper(),
        "mollie_status": status,
        "ttl": create_ttl_year(),
        "creation_date": resp_data.get("createdAt"),
    }
    mollie_table.put_item(Item=mollie_data)
    return {"isBase64Encoded": False, "statusCode": 200, "body": "{}"}


def retrieve_mollie_data(event, config):
    query_str_params = get(event, "queryStringParameters", {})
    payment_type = query_str_params.get("PaymentType")
    order_id = query_str_params.get("OrderId")
    payment_id = query_str_params.get("PaymentId")

    if not (payment_id or (payment_type and order_id)):
        raise BadRequestError(
            "PaymentId or OrderId + PaymentType is required",
            error_code="MISSING_QUERY_PARAMS",
        )

    if payment_id:
        response = mollie_table.get_item(
            Key={"payment_id": payment_id},
            ProjectionExpression="payment_id, creation_date, order_id, mollie_status, payment_type",
        )
        data = response.get("Item")
    else:
        response = mollie_table.query(
            IndexName="order_id-index",
            KeyConditionExpression=Key("order_id").eq(order_id.zfill(12)),
            FilterExpression=Attr("payment_type").eq(payment_type),
            ProjectionExpression="payment_id, creation_date, order_id, mollie_status, payment_type",
        )["Items"]

        data = first(sorted(response, key=lambda x: x["creation_date"], reverse=True))

    if data is None:
        raise NotFoundError("No data with given ID", error_code="NO_DATA_FROM_ID")

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": capitalizeKeys(data),
    }


def post_mollie_waiting(event, config):
    query_str_params = get(event, "queryStringParameters", {})
    payment_id = query_str_params.get("PaymentId")
    dossier_id = payment_type = status = creation_date = None

    if not payment_id:
        raise BadRequestError(
            "PaymentId is required",
            error_code="MISSING_QUERY_PARAMS",
        )

    response = mollie_table.get_item(Key={"payment_id": payment_id})
    item = response.get("Item")

    if not item:
        with contextlib.suppress(Exception):
            resp = requests.get(
                url=f"https://api.mollie.com/v2/payments/{payment_id}",
                headers={"Authorization": f"Bearer {get_secret_string(environ['MOLLIE_TOKEN'])}"},
                timeout=10,
            )
            resp_data = resp.json()

            dossier_id = (resp_data.get("metadata") or {}).get("dossier")
            payment_type = (resp_data.get("metadata") or {}).get("type")
            status = resp_data.get("status")
            creation_date = resp_data.get("createdAt")

        item = {
            "payment_id": payment_id,
            "order_id": dossier_id if dossier_id else "UNKNOWN",
            "payment_type": payment_type if payment_type else "UNKNOWN",
            "mollie_status": status if status and status != "open" else "waiting",
            "ttl": create_ttl_year(),
            "creation_date": creation_date if creation_date else datetime.now(UTC).replace(microsecond=0).isoformat(),
        }

        mollie_table.put_item(Item=item)

    return {
        "isBase64Encoded": False,
        "statusCode": 200,
        "body": capitalizeKeys(item),
    }
