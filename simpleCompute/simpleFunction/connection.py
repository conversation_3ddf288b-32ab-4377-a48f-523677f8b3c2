import io
import json
import os
from concurrent import futures
from urllib.parse import urlparse

import requests
from marshmallow import ValidationError

from utils.api import api_caller
from utils.dict_utils import get
from utils.errors import InternalServerError, BadRequestError
from utils.log_utils import log_info, log_err
from utils.models.connection_model import Connection
from utils.sentry.sentry_utils import capture_message_in_sentry


def create(event, context):
    body = event.get("body")
    headers = event.get("headers")

    data = json.loads(body)

    if not headers.get("SessionId") and not headers.get("Authorization"):
        raise BadRequestError("SessionId or Bearer token not provided", error_code="MISSING_SESSIONID_OR_BEARER")

    try:
        raccordement_data_from_body: Connection = Connection.schema().loads(body)
    except ValidationError as e:
        raise BadRequestError("Some required fields are missing or have an incorrect type", error_code="INVALID_FIELDS", extra=e.messages) from e

    sap_raccordement_data = raccordement_data_from_body.to_sap_format_ws35()
    response_async_demande_travaux = api_caller(
        "post",
        "/asynchrone",
        headers=headers,
        body={"Method": "post", "Path": "/demande_travaux/demande", "Type": "external", "Data": json.dumps(sap_raccordement_data)},
    )
    process_id = response_async_demande_travaux.get("ProcessId", None)

    with futures.ThreadPoolExecutor() as executor:

        def process_meter(_meter):
            file_url = _meter.photo
            response_fichier_upload = api_caller(
                "get",
                "/fichiers/upload",
                headers=headers,
                params={
                    "DocumentName": f"photo_compteur_{_meter.number}_{_meter.ean}{get_file_extension(file_url)}",
                    "IdDossier": f"{process_id}_{raccordement_data_from_body.sect_activite}",
                },
            )

            put_url = response_fichier_upload.get("PutUrl", None)

            file_content = download_file_in_memory(file_url)
            upload_file_in_memory(put_url, file_content)

        for meter in raccordement_data_from_body.meters:
            if meter.photo:
                executor.submit(process_meter, meter)

    if data["WorkType"] == "MODI_SMART":
        for meter in data["Meters"]:
            mail_data = {
                "num_ean": get(meter, "Ean"),
                "num_cpt": get(meter, "Number"),
                "surname": get(data, "Name"),
                "name": get(data, "Firstname"),
                "email": get(data, "Email"),
                "phone": get(data, "Phone"),
                "adresse": f"{get(data.get('Address', {}), 'Street')} {get(data.get('Address', {}), 'Number')} {get(data.get('Address', {}), 'Postcode')} {get(data.get('Address', {}), 'City')}",
                "act_as": get(data, "ActAs"),
                "contact_enterprise_surname": get(data, "Name"),
                "contact_enterprise_name": get(data, "Firstname"),
                "contact_enterprise_mail": get(data, "Email"),
                "contact_enterprise_phone": get(data, "Phone"),
                "enterprise_name": get(data.get("Company", {}), "Name"),
                "enterprise_legal_status": get(data.get("Company", {}), "LegalStatus"),
                "enterprise_vat_num": get(data.get("Company", {}), "Tva"),
                "enterprise_adresse": f"{get(data.get('Contact', {}), 'Street')} {get(data.get('Contact', {}), 'Number')} {get(data.get('Contact', {}), 'Postcode')} {get(data.get('Contact', {}), 'City')}",
                "enterprise_vat_subject": "Yes" if get(data.get("Company", {}), "Tva") else "No",
                "is_picture": "Yes" if get(meter, "Photo") else "No",
            }
            api_caller(
                method="post",
                path="/envMessage",
                body={
                    "Langue": os.environ["LANG"],
                    "Header": {"TEMPLATE_ID": "REQUEST_RACC_DIGITAL", "EMAIL": data["Email"], "NO_USER_CHECK": "Y"},
                    "Content": mail_data,
                },
            )

    return {"statusCode": 204}


def get_file_extension(url):
    parsed_url = urlparse(url)

    file_path = parsed_url.path

    _, file_extension = os.path.splitext(file_path)

    return file_extension


def download_file_in_memory(get_url):
    try:
        response = requests.get(get_url)
        response.raise_for_status()  # Raises an exception for HTTP errors
        file_content = io.BytesIO(response.content)
        return file_content
    except requests.exceptions.RequestException as e:
        error_message = f"Erreur lors du téléchargement du fichier, code : {response.status_code if response else 'N/A'}"
        extra_info = {"url": get_url, "status_code": response.status_code if response else "N/A", "response_text": response.text if response else str(e)}
        capture_message_in_sentry(msg=error_message, extra=extra_info, level="error")

        raise InternalServerError(message=error_message, error_code="DOWNLOAD_ERROR") from e


def upload_file_in_memory(put_url, file_in_memory):
    try:
        file_in_memory.seek(0)
        headers = {"Content-Type": "*"}
        response = requests.request("PUT", put_url, headers=headers, data=file_in_memory)
        if response.status_code in [200, 201, 204]:
            log_info("Fichier uploadé avec succès.")
        else:
            error_message = f"Erreur lors de l'upload du fichier, code : {response.status_code}"
            extra_info = {"url": put_url, "status_code": response.status_code, "response_text": response.text}
            capture_message_in_sentry(msg=error_message, extra=extra_info, level="error")

            # Log de l'erreur
            log_err(f"Upload error: {error_message}, Extra: {extra_info}")

            raise InternalServerError(message=error_message, error_code="UPLOAD_ERROR")

    except Exception as e:
        log_err(f"Unexpected error during file upload: {str(e)}")
        capture_message_in_sentry(msg="Unexpected error during file upload", extra={"exception": str(e)}, level="error")
        raise InternalServerError(message="Erreur interne lors de l'upload", error_code="UPLOAD_ERROR")
