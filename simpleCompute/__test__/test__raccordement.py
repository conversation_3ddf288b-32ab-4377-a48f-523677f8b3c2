import io
import unittest
from unittest.mock import patch, MagicMock

from simpleFunction.connection import create, get_file_extension, upload_file_in_memory
from utils.errors import BadRequestError, InternalServerError
from utils.mocking import mock_environnement
from utils.models.connection_model import Connection


class TestConnectionCreate(unittest.TestCase):
    @mock_environnement
    @patch("simpleFunction.connection.api_caller")
    def test_handler_success(self, mock_api_caller):
        # Mock the necessary external dependencies
        event = {
            "body": '{"EnergyType": "ELEC", "WorkType": "MODI_SMART", "ApplicantType": "particulier", "ActAs": "PROP", "Name": "Doe", "Firstname": "<PERSON>", "Email": "<EMAIL>", "Phone": "+32 11 22 33 44", "Address": {"Street": "Rue de l\'Industrie", "Number": "12B", "Postcode": "1000", "City": "Bruxelles", "Box": "A"}, "Contact": {"Street": "Rue de la Gare", "Number": "45", "Postcode": "5000", "City": "Namur", "Country": "Belgique", "Box": "2"}, "Billing": {"Street": "Avenue des Champs", "Number": "78", "Postcode": "1050", "City": "Ixelles", "Country": "Belgique"}, "Meters": [{"Ean": "5414789632156", "Number": "*********", "Photo": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}], "Company": {"Name": "TestCompany", "LegalStatus": "SA", "Tva": "BE*********"}}',
            "headers": {"Accept-Language": "fr", "SessionId": "mock_session_id"},
        }

        context = {}

        # Mock api_caller responses
        mock_api_caller.side_effect = [
            {"ProcessId": "12345"},
            {"PutUrl": "https://httpbin.org/put"},
            {},
        ]

        # Call handler
        response = create(event, context)

        self.assertEqual(response["statusCode"], 204)
        mock_api_caller.assert_called()

    def test_verify_body_elems_missing_fields(self):
        with self.assertRaises(BadRequestError) as ex:
            create(
                {
                    "body": '{"EnergyType": "ELEC", "WorkType": "MODI_SMART", "Name":53}',
                    "headers": {"Accept-Language": "fr", "SessionId": "mock_session_id"},
                },
                {},
            )
        self.assertDictEqual(
            ex.exception.extra,
            {
                "ApplicantType": ["Missing data for required field."],
                "ActAs": ["Missing data for required field."],
                "Name": ["Not a valid string."],
                "Firstname": ["Missing data for required field."],
                "Email": ["Missing data for required field."],
                "Phone": ["Missing data for required field."],
                "Address": ["Missing data for required field."],
                "Meters": ["Missing data for required field."],
            },
        )

    def test_verify_body_elems_all_fields(self):
        body = '{"EnergyType": "ELEC", "WorkType": "MODI_SMART", "ApplicantType": "particulier", "ActAs": "PROP", "Name": "Doe", "Firstname": "John", "Email": "<EMAIL>", "Phone": "+32 11 22 33 44", "Address": {"Street": "Rue de l\'Industrie", "Number": "12B", "Postcode": "1000", "City": "Bruxelles"}, "Meters": [{"Ean": "5414789632156", "Number": "*********", "Photo": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}]}'
        try:
            Connection.from_json(body)  # This should not raise an exception
        except BadRequestError:
            self.fail("verify_body_elems raised BadRequestError unexpectedly!")

    def test_get_file_extension(self):
        url = "https://example.com/file.jpg"
        extension = get_file_extension(url)
        self.assertEqual(extension, ".jpg")

    @patch("requests.put")
    def test_upload_file_in_memory_error(self, mock_put):
        # Mock the response of requests.put to simulate an error
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_put.return_value = mock_response

        file_in_memory = io.BytesIO(b"file content")

        with self.assertRaises(InternalServerError):
            upload_file_in_memory("https://example.com/put", file_in_memory)
