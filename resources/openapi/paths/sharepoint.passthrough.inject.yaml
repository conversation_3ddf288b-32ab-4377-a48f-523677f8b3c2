post:
  summary: "WS216 : Passthrough to SharePoint operations WS."
  description: |
    Provides SharePoint operations (import, update, move, delete) according to passed JSON request.  
    Source WS Sharepoint documentation : [here](https://dev.azure.com/RESABE/Valises%20SharePoint/_wiki/wikis/Valises-SharePoint.wiki/282)
  requestBody:
    description: "Request body for SharePoint operations, see the [source WS Sharepoint documentation](https://dev.azure.com/RESABE/Valises%20SharePoint/_wiki/wikis/Valises-SharePoint.wiki/282) for details"
    content:
      application/json:
        schema:
          type: object
  responses:
    '200':
      description: Response from SharePoint operation according to the passed request body
      content:
        application/json:
          schema:
            type: object
            properties:
              isError:
                type: boolean
                description: True or false if error occurred during a call.
                example: true/false
                required: true
              message:
                type: string
                description: User friendly error message if occurred.
                example: Some useful message
                required: true
              status:
                type: number
                description: Optional if error occurred error number.
                example: 403
                required: false
              results:
                type: array
                description: Optional if no error occurred, results, according to request.
                example:
                  - id: 0
                    m: message
                required: false
