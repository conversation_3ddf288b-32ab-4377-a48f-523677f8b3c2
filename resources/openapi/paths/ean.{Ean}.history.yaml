get:
  summary: "WS219 : Récupérer les historiques d'index d'un EAN donné."
  security:
    - tokenAuthorizer: [ ]
  parameters:
    - name: Ean
      in: path
      required: true
      schema:
        type: string
  responses:
    '200':
      description: OK
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                Date:
                  type: string
                  example: "2022-05-30"
                Index:
                  type: number
                  example: 5195.452
                Consumption:
                  type: number
                  example: 34.633
                Register:
                  type: string
                  example: "NUIT"
                RegisterCode:
                  type: string
                  example: "1.8.2"
                ReadingReason:
                  type: string
                  example: "Relevé de résiliation emméngt/déméngt"
                ReadingType:
                  type: string
                  example: "Relevé par l'EPTD"
                Supplier:
                  type: string
                  example: "EDF LUMINUS Electricité"
    '403':
      description: Forbidden - The EAN doesn't belong to the given user.
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: "This EAN doesn't belong to the given user."
              error_code:
                type: string
                example: "INVALID_EAN_FOR_USER"