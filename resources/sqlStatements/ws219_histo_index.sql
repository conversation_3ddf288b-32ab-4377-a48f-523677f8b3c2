WITH bp_hierarchy AS (
    SELECT * FROM HIERARCHY(
        SOURCE(
            SELECT PARTNER1 AS parent_id, PARTNER2 AS node_id FROM {HanaSchema}.BUT050 WHERE PARTNER1 = :Bp AND RELTYP = 'BUR998'
        )
        START WHERE PARTNER1 in (SELECT PARTNER FROM {HanaSchema}.BUT000 WHERE BPKIND = 'MYRE' AND XDELE = '' AND XBLCK = '')
    )
),
find_root as (
    SELECT item.node_id AS bp, root.parent_id AS root FROM bp_hierarchy item JOIN bp_hierarchy root ON root.HIERARCHY_RANK = item.HIERARCHY_ROOT_RANK
    UNION ALL
    SELECT DISTINCT parent_id AS bp, parent_id AS root FROM bp_hierarchy WHERE HIERARCHY_PARENT_RANK = 0
),
get_ean_contract as (
    SELECT DISTINCT EUITRANS.EXT_UI AS EAN, EVER.*, EVER.INVOICING_PARTY AS Supplier
    FROM find_root
    INNER JOIN {HanaSchema}.FKKVKP ON FKKVKP.GPART = bp
    INNER JOIN {HanaSchema}.EVER ON EVER.VKONTO = FKKVKP.VKONT
    INNER JOIN {HanaSchema}.EUIINSTLN ON EUIINSTLN.ANLAGE = EVER.ANLAGE
    INNER JOIN {HanaSchema}.EUITRANS ON EUITRANS.INT_UI = EUIINSTLN.INT_UI
    WHERE EUITRANS.EXT_UI = :Ean
)
SELECT DISTINCT
	TE609T.TEXT40 AS MotifReleve,
    g.EAN,
    ESERVPROVT.SP_NAME AS SupplierName,
    TE614T.TEXT40 as TypeReleve,
    EABL.ADAT as DateReleve,
    EABL.V_ZWSTAND as ReleveIndexRound,
    EABL.N_ZWSTAND as ReleveIndexDecimal,
    EASTS.TARIFART as CatTarif,
    ETDZ.ZWART AS CatCadran,
    ETDZ.KENNZIFF as CodeCadran,
    ETDZ.ZWNUMMER,
    AUSP.ATWRT,
    EZWG.ANZART,
    COUNT(DISTINCT ETDZ.ZWNUMMER) OVER (PARTITION BY ETDZ.EQUNR, EABL.ADAT) AS "lv_nb_lines"
FROM get_ean_contract g
INNER JOIN {HanaSchema}.EABLG ON EABLG.ANLAGE = g.ANLAGE
    AND EABLG.ADATSOLL >= g.EINZDAT
    AND EABLG.ADATSOLL <= g.AUSZDAT
    AND EABLG.UNTERDR <>'X'
INNER JOIN {HanaSchema}.EABL ON EABLG.ABLBELNR = EABL.ABLBELNR
    AND EABL.ABLSTAT <> '0'
    AND EABL.ADAT >= TO_CHAR(ADD_MONTHS(CURRENT_DATE, -36), 'YYYYMMDD')
INNER JOIN {HanaSchema}.ETDZ ON EABL.EQUNR = ETDZ.EQUNR
    AND EABL.ZWNUMMER = ETDZ.ZWNUMMER
    AND ETDZ.AB < g.AUSZDAT
    AND ETDZ.BIS > g.EINZDAT
INNER JOIN {HanaSchema}.EASTS ON ETDZ.LOGIKZW = EASTS.LOGIKZW
    AND EASTS.ANLAGE = g.ANLAGE
    AND EASTS.AB < g.AUSZDAT
    AND EASTS.BIS > g.EINZDAT
INNER JOIN {HanaSchema}.EGERH ON ETDZ.EQUNR = EGERH.EQUNR 
INNER JOIN {HanaSchema}.EZWG ON ETDZ.ZWNUMMER = EZWG.ZWNUMMER 
	AND EGERH.ZWGRUPPE = EZWG.ZWGRUPPE
INNER JOIN {HanaSchema}.EQUI ON EABL.EQUNR = EQUI.EQUNR
LEFT JOIN {HanaSchema}.AUSP ON EQUI.MATNR = AUSP.OBJEK
    AND AUSP.ATINN = '0000000193'
    LEFT JOIN {HanaSchema}.TE609T ON TE609T.ABLESGR = EABLG.ABLESGR AND TE609T.SPRAS = LEFT (:Langue, 1)
    LEFT JOIN {HanaSchema}.TE614T ON TE614T.ABLESART = EABL.ABLESTYP AND TE614T.SPRAS = LEFT (:Langue, 1)
    LEFT JOIN {HanaSchema}.ESERVPROVT ON ESERVPROVT.SERVICEID = g.Supplier AND ESERVPROVT.SPRAS = 'F'
WHERE
    EABLG.ABLESGR IN ('01'
    , '02'
    , '03')                                -- Only keep billing entry
  AND EABL.V_ZWSTAND + EABL.N_ZWSTAND != 0 -- Remove inactive register
ORDER BY EABL.ADAT DESC, ETDZ.ZWNUMMER ASC;