import json

from utils.errors import BadRequestError


def validation(request, _):
    if not request.data:
        raise BadRequestError("Body content is missing", error_code="NO_BODY")
    data = json.loads(request.data)

    try:
        if not data.get("Demande"):
            raise KeyError("Demande")

        adresse = data.get("Adresse", {})
        if not adresse:
            raise KeyError("Adresse")
        if not adresse.get("Pays"):
            raise KeyError("Adresse.Pays")
        if not adresse.get("CdPostal"):
            raise KeyError("Adresse.CdPostal")
        if not adresse.get("Localite"):
            raise KeyError("Adresse.Localite")
        if not adresse.get("Rue"):
            raise KeyError("Adresse.Rue")
        if not adresse.get("ParcelleSpw") and not adresse.get("Emplacement") and not adresse.get("NumRue"):
            raise KeyError("Adresse.NumRue")

        partenaires = data.get("Partenaire", [])
        if not partenaires:
            raise KeyError("Partenaire")

        for idx, partner in enumerate(partenaires):
            if partner.get("TypePartenaire") == "DEMANDEUR":
                if not partner.get("Nom"):
                    raise KeyError(f"Partenaire.{idx}.Nom")
                if not partner.get("Prenom"):
                    raise KeyError(f"Partenaire.{idx}.Prenom")
                if not partner.get("Gsm"):
                    raise KeyError(f"Partenaire.{idx}.Gsm")
                if not partner.get("Email"):
                    raise KeyError(f"Partenaire.{idx}.Email")

                partner_adresse = partner.get("Adresse", {})
                if not partner_adresse:
                    raise KeyError(f"Partenaire.{idx}.Adresse")
                if not partner_adresse.get("Rue"):
                    raise KeyError(f"Partenaire.{idx}.Adresse.Rue")
                if not partner_adresse.get("NumRue"):
                    raise KeyError(f"Partenaire.{idx}.Adresse.NumRue")
                if not partner_adresse.get("CdPostal"):
                    raise KeyError(f"Partenaire.{idx}.Adresse.CdPostal")
                if not partner_adresse.get("Localite"):
                    raise KeyError(f"Partenaire.{idx}.Adresse.Localite")
                if not partner_adresse.get("Pays"):
                    raise KeyError(f"Partenaire.{idx}.Adresse.Pays")
    except KeyError as e:
        raise BadRequestError(
            f"Required field '{e.args[0]}' is missing or empty",
            error_code="CONTENT_MISSING",
        )

    return request
