import json
from io import Bytes<PERSON>

from classes import Request

from utils.dict_utils import case_insensitive_dict
from utils.errors import BadRequestError


def notEmpty(request, params):
    data = None
    try:
        data = json.loads(request.data or "{}")
    except ValueError:
        data = request.data
    if not data:
        raise BadRequestError("Request body cannot be empty")
    return request


def to_file(request: Request, params):
    headers = case_insensitive_dict(request.headers)
    files = [("files", (params.get("file_name", "file"), BytesIO(request.data.encode("utf-8")), headers["content-type"]))]
    request.files = files
    request.data = None
    return request
