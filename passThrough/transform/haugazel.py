import copy
import json
import os

import boto3
from apply_transform import apply_transform
from classes.MergableRequest import MergableRequest

from utils.api import api_caller
from utils.auth_utils import getUserData
from utils.dict_utils import get
from utils.errors import ForbiddenError, HttpError
from utils.models.user import User
from utils.parallel import concurrently
from utils.userdata import Ean

dynamodb = boto3.resource("dynamodb")
table = dynamodb.Table(os.environ["DYNAMODB"])


def injectPartenaireId(request, params):
    user = User.from_event(request.__dict__)

    haugazel_id_list = list(
        filter(
            None,
            set(concurrently(*[lambda ean=ean: api_caller(method="get", path=f"/ean/{ean}", headers=request.headers).get("BpHgz") for ean in user.ean_ids])),
        ),
    )

    requests = []
    for hid in haugazel_id_list:
        subreq = copy.deepcopy(request)
        subreq.path_parameters[params["path"]] = int(hid)
        requests.append(subreq)

    def onMerge(resps):
        print("params", params)
        resps = [apply_transform(resp, get(params, "transform", [])) for resp in resps]
        if not resps:
            raise HttpError(404, "No HaugazelId found for this user")
        resps = [resp for resp in resps if resp.statusCode == 200]
        if not resps:
            raise HttpError(404, "Not found")
        print("responses", [resp.toDict() for resp in resps])
        ret = resps[0]
        ret.body = json.dumps([json.loads(ret.body)])
        for resp in resps[1:]:
            array = json.loads(ret.body)
            array.append(json.loads(resp.body))
            ret.body = json.dumps(array)
        return ret

    return MergableRequest(requests, onMerge)


def injectNumeroContrat(request, params):
    user_data = getUserData({"headers": request.headers}, allow_ghost=False)
    if not user_data:
        raise ForbiddenError()
    haugazelID_list = [str(int(ean_obj["NumeroContrat"])) for ean_obj in Ean(user_data).get(enrich_data=True) if ean_obj["NumeroContrat"]]

    requests = []
    for hid in haugazelID_list:
        subreq = copy.deepcopy(request)
        subreq.path_parameters[params["path"]] = int(hid)
        requests.append(subreq)

    def onMerge(resps):
        print("params", params)
        resps = [apply_transform(resp, get(params, "transform", [])) for resp in resps]
        if not resps:
            raise HttpError(404, "No HaugazelId found for this user")
        resps = [resp for resp in resps if resp.statusCode == 200]
        if not resps:
            raise HttpError(404, "Not found")
        print("responses", [resp.toDict() for resp in resps])
        ret = resps[0]
        ret.body = json.dumps([json.loads(ret.body)])
        for resp in resps[1:]:
            array = json.loads(ret.body)
            array.append(json.loads(resp.body))
            ret.body = json.dumps(array)
        return ret

    return MergableRequest(requests, onMerge)
