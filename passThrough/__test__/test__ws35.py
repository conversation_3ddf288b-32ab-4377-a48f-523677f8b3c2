import json
import unittest
from types import SimpleNamespace

from transform.ws35 import validation


class TestValidationFunction(unittest.TestCase):
    def setUp(self):
        self.request_data = {
            "Demande": "example",
            "Adresse": {
                "Pays": "Belgium",
                "CdPostal": "1000",
                "Localite": "Brussels",
                "Rue": "Main Street",
                "NumRue": "42",
            },
            "Partenaire": [
                {
                    "TypePartenaire": "DEMANDEUR",
                    "Nom": "<PERSON>",
                    "Prenom": "Doe",
                    "Gsm": "0123456789",
                    "Email": "<EMAIL>",
                    "Adresse": {
                        "Rue": "Main Street",
                        "NumRue": "42",
                        "CdPostal": "1000",
                        "Localite": "Brussels",
                        "Pays": "Belgium",
                    },
                }
            ],
        }

    def test_validation_with_invalid_data(self):
        fields_to_test = ["Demande", "Adresse", "Partenaire"]

        for field in fields_to_test:
            # <PERSON>uvegardez la valeur originale
            original_value = self.request_data[field]

            # Mettez la valeur à None
            self.request_data[field] = None

            request = SimpleNamespace(data=json.dumps(self.request_data))

            # s'attendre à ce que la fonction validation() lève une exception
            with self.assertRaises(Exception):
                validation(request, None)

            # Restaurez la valeur originale pour le prochain cycle
            self.request_data[field] = original_value

    def test_valid_request(self):
        request = SimpleNamespace(data=json.dumps(self.request_data))

        try:
            validation(request, None)
        except Exception as e:
            self.fail(f"validation() raised {e} unexpectedly!")
