import contextlib
import json
from dataclasses import dataclass, field
from urllib.parse import urlparse

from requests import request

from classes.Response import Response
from utils.DecimalEncoder import DecimalEncoder
from utils.log_utils import log_err_json
from utils.sentry.sentry_utils import capture_exception_in_sentry


@dataclass
class Request:
    """
    Represents an HTTP request with customizable properties and transformation capabilities.

    This class encapsulates the components of an HTTP request including method,
    URL, headers, parameters, and more. It provides utility functions to transform
    the request using a given function and to execute asynchronous HTTP calls.
    Primarily designed to simplify and standardize HTTP request construction and
    execution.

    Attributes
    ----------
    method : str
        HTTP request method (e.g., 'GET', 'POST').
    url : str
        Target endpoint URL for the HTTP request.
    params : dict
        Query parameters to include in the HTTP request.
    headers : dict
        HTTP headers to include in the request.
    path_parameters : dict
        URL path parameters for dynamic URL construction.
    data : str
        Request body data (e.g., for POST/PUT requests).
    auth : tuple
        Authentication credentials for the HTTP request.
    files : dict
        Files to include in the HTTP request, typically for multipart uploads.

    """

    method: str
    url: str
    params: dict = field(default_factory=dict)
    headers: dict = field(default_factory=dict)
    path_parameters: dict = field(default_factory=dict)
    data: str = ""
    auth: tuple[str, str] = field(default_factory=tuple)
    files: dict = None

    def transform(self, function, args=None):
        if args is None:
            args = {}
        return function(self, args)

    async def execute(self) -> Response:
        url = self.url.format(**self.path_parameters)
        print(
            "execute request : ",
            self.method,
            " ",
            url,
            "\nparams : ",
            self.params,
            "\nheaders : ",
            self.headers,
            "\ndata : ",
            self.data,
        )

        json_data = None
        with contextlib.suppress(Exception):
            json_data = json.loads(self.data)

        resp = None
        try:
            resp = request(
                method=self.method.upper(),
                url=url,
                params=self.params,
                headers={**self.headers, "Host": urlparse(url).netloc},
                data=self.data if not json_data else None,
                json=json_data,
                auth=self.auth,
                verify=False,
                files=self.files,
            )
            print("resp.encoding", resp.encoding)
            headers = json.loads(json.dumps(dict(resp.headers), cls=DecimalEncoder))
            headers["content-encoding"] = None  # Quick fix ?
            return Response(self, False, resp.status_code, headers, resp.text)
        except Exception as e:
            # Unhandled error
            log_err_json(e)
            capture_exception_in_sentry(e)
        finally:
            if resp:
                resp.close()
