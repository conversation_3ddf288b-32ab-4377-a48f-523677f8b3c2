locals {
  endpoint_ttl = {
    "adresse/GET"                = 60 * 15
    "adresse/ean/GET"            = 60 * 15
    "adresse/services/GET"       = 60 * 15
    "adresse/cdpostaux/GET"      = 60 * 15
    "ep/GET"                     = 60 * 15
    "pannes/planned/GET"         = 60 * 15
    "pannes/unplanned/GET"       = 60 * 15
    "index/passage/GET"          = 60 * 15
    "delestage/pour_adresse/GET" = 60 * 15
    "cab/point_rechargement/GET" = 60 * 15
    "jobs/GET"                   = 60 * 15
    "raccordement/forfaits/GET"  = 60 * 15
  }
}


resource "aws_api_gateway_method_settings" "settings" {
  depends_on = [aws_api_gateway_stage.MyResaAPI_Stage]
  for_each   = local.endpoint_ttl

  rest_api_id = aws_api_gateway_rest_api.MyResaAPI.id
  stage_name  = local.workspace["api_stage"]
  method_path = each.key

  settings {
    caching_enabled                         = true
    cache_ttl_in_seconds                    = each.value
    require_authorization_for_cache_control = false
  }
}
