FROM public.ecr.aws/lambda/python:3.12

# Install pango for weasyprint
RUN dnf install -y pango

# Copy lambda code
COPY ./transformHtmlToPdf ${LAMBDA_TASK_ROOT}/tmp_build
COPY ./utils ${LAMBDA_TASK_ROOT}/tmp_build/utils
COPY ./global_requirements.txt ${LAMBDA_TASK_ROOT}

# Install python packages
WORKDIR ${LAMBDA_TASK_ROOT}/tmp_build
RUN pip install uv && uv pip install --system -r requirements.txt

# Move everything to the lambda task root
WORKDIR ${LAMBDA_TASK_ROOT}
RUN mv ./tmp_build/* ./ &&\
 rm -rf ./tmp_build

# Set the CMD to your handler
CMD [ "transformHtmlToPdf.handler" ]