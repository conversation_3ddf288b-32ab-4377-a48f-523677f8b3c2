[project]
name = "aws-api-web"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "~=3.11"
dependencies = []

[tool.ruff]
line-length = 180

[tool.ruff.lint]
select = [
    "ANN", # flake8-annotations
    "E", "W", # pycodestyle
    "F", # Pyflakes
    "UP", # pyupgrade
    "B", # flake8-bugbear
    "Q", # flake8-quotes
    "SIM", # flake8-simplify
    "I", # isort
    "S", # flake8-bandit
    "COM", # flake8-commas
    "C4", # flake8-comprehensions
    "PIE", #flake8-pie
    "RET501", "RET502", "RET503", "RET504", # flake8-return
    "C90", # mccabe
    "N", # pep8-naming
    "PERF", # Perflint
    "D", # pydocstyle
    "PL", # pylint
    "FURB", # refurb
    "RUF", # Ruff-specific rules
    "TRY", # tryceratops
]
ignore = [
    "D100",
    "D203", # conflicting with D211
    "D212", # conflicting with D213
    "PLC0206", # conflicting with SIM118
    "D100",
    "D107",
]