import os
from copy import deepcopy
from multiprocessing.pool import ThreadPool

from boto3.dynamodb.conditions import Attr, Key
from controllers.SynchroAccountData import SynchroAccountData

from sqlDriven import run_main_query
from utils.aws_utils import scan_all_generator


class TriggerSynchroAccount:
    def __init__(self, event, _):
        self.event = event
        self.linking = {
            "type": "sqlDriven",
            "secret": "{HANA_SECRET}",
            "controller": "SynchroAccountData",
            "sql_template": {
                "user_data": "sqlStatements/ws14/ws14_user_data.sql",
                "eans": "sqlStatements/ws14/ws14_eans.sql",
                "dossiers": "sqlStatements/ws14/ws14_dossiers.sql",
                "preferences": "sqlStatements/ws14/ws14_preferences.sql",
            },
            "params": {},
            "return": {
                "type": "list",
            },
        }
        self.account_list = scan_all_generator(
            os.environ["DYNAMODB"],
            {
                "ProjectionExpression": "uid",
                "FilterExpression": ~Key("uid").begins_with("ghost_") & Attr("valide").eq(True) & Attr("bp").exists(),
            },
        )

    def process(self):
        with ThreadPool(processes=10) as thread_pool:
            for account in self.account_list:
                self.event["uid"] = account["uid"]
                thread_pool.apply(run_main_query, args=(SynchroAccountData(self.event, self.linking),))
                run_main_query(
                    SynchroAccountData(
                        deepcopy({{**self.event, "uid":account["uid"]}}),
                        self.linking,
                    )
                )
                
