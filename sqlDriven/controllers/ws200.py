from dataclasses import dataclass
from os import environ
from typing import Optional

from dataclasses_json import LetterCase, dataclass_json

from controllers.NoPagination import NoPagination
from utils.dict_utils import get
from utils.errors import UnauthorizedError, BadRequestError
from utils.models.address import Address, ADDRESS_EXCLUDE
from utils.models.ean_meter_info import EanMeters, EanAddressInfo, Meter
from utils.models.user import User
from utils.u_codes import UCode


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class WS200Input:
    ean: Optional[str] = None
    meter_id: Optional[str] = None
    user: Optional[User] = None

    @classmethod
    def from_event(cls, event: dict) -> "WS200Input":
        obj: "WS200Input" = cls.from_dict(get(event, "queryStringParameters", {}))

        try:
            obj.user = User.from_event(event, allow_ghost=False)
        except UnauthorizedError as ex:
            obj.user = None
            if ex.error_code == "INVALID_TOKEN":
                raise ex

        return obj


class ws200(NoPagination):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.input = WS200Input.from_event(event)

    def validate_request_params(self):
        input_params = super().validate_request_params()

        if not self.input.user and (not self.input.ean or not self.input.meter_id):
            raise BadRequestError(
                "Missing mandatory query string when non auth: [Ean, MeterId]",
                error_code="MISSING_PARAMETERS",
            )

        return input_params

    def apply_hana_env(self, sql_statement):
        """
        Specifies the Hana schema for Hana SQL environments
        """
        ean_list = ""
        if self.input.user:
            ean_list = "'" + "','".join(self.input.user.ean_ids) + "'"
        else:
            ean_list = "'" + self.input.ean + "'"

        sql_statement = sql_statement.format(HanaSchema=environ["HANA_SCHEMA"], Ean=ean_list)

        return sql_statement

    def to_response(self, cursor, params=None):
        db_result = super().to_response(cursor, params)

        if not self.input.user:
            counter_ids = [row["Meter_Number"].zfill(18) for row in db_result]
            if self.input.meter_id.zfill(18) not in counter_ids:
                raise BadRequestError(
                    "The given Ean doesn't match the MeterId",
                    error_code="INCORRECT_PARAMETERS",
                )

        # from db_result, group data by adress then by ean
        grouped_db_result = {}
        for row in db_result:
            address = Address(
                street=row["Address_Street"], number=row["Address_Number"], postcode=row["Address_Poscode"], city=row["Address_City"], box=ADDRESS_EXCLUDE, country=ADDRESS_EXCLUDE
            )
            meter = Meter(
                type="elec" if row["Meter_Type"] == "01" else "gaz",
                number=row["Meter_Number"],
                power=row["Meter_Power"],
                amper=row["Meter_Amper"],
                tarif=UCode(row["Meter_Tarif"] if row["Meter_Type"] == "01" else "Gaz").desc,
                production=row["Meter_Production"],
                smart=row["Meter_Smart"],
            )
            if address not in grouped_db_result:
                grouped_db_result[address] = {}
            if row["Ean"] not in grouped_db_result[address]:
                grouped_db_result[address][row["Ean"]] = EanMeters(row["Ean"])
            grouped_db_result[address][row["Ean"]].meters.append(meter)

        return [EanAddressInfo(address, [ean for ean in eans.values()]).to_dict() for address, eans in grouped_db_result.items()]
