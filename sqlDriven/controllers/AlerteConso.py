import os
from collections import defaultdict
from datetime import datetime, timedelta
from os import environ

import pytz
from boto3.dynamodb.conditions import Attr
from controllers.NoPagination import NoPagination
from dateutil.relativedelta import relativedelta

from utils.api import api_caller, basic_auth_headers
from utils.aws_utils import get_dynamodb_table
from utils.errors import NotFoundError
from utils.log_utils import log_info
from utils.models.histo_alert_model import HistoricAlert


class AlerteConso(NoPagination):
    RED_TABLE_ELEC = "public.cumulativeactiveenergyp1d"
    RED_TABLE_GAZ = "public.cumulativestoredvolumep1d"
    DYNAMO_ALERT_TABLE = get_dynamodb_table(os.environ["SmartConsoAlerts"])
    DYNAMO_USER_TABLE = get_dynamodb_table(os.environ["DYNAMODB"])
    DYNAMO_HISTORIC_ALERT_TABLE = get_dynamodb_table(os.environ["HistoricAlerts"])
    TEMPLATE_NAME = "ALERTE_CONSO_V2"

    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.current_date = datetime.now(pytz.timezone("Europe/Brussels"))
        self.start_date = self.current_date - timedelta(days=3)
        self.end_date = self.current_date - timedelta(days=2)
        self.monthly = self.current_date.day == 3
        if self.monthly:
            self.start_date_monthly = self.end_date - relativedelta(months=1)
        self.items = self.get_all_table_items()

    def get_all_table_items(self):
        filter_condition = Attr("Daily").exists() & Attr("Daily").ne(None)
        if self.monthly:
            filter_condition |= Attr("Monthly").exists() & Attr("Monthly").ne(None)

        response = self.DYNAMO_ALERT_TABLE.scan(FilterExpression=filter_condition)
        items = response.get("Items", [])
        while "LastEvaluatedKey" in response:
            response = self.DYNAMO_ALERT_TABLE.scan(FilterExpression=filter_condition, ExclusiveStartKey=response["LastEvaluatedKey"])
            items.extend(response.get("Items", []))
        items_dict = {
            item["Meter"]: {"Uid": item.get("Uid"), "Ean": item.get("Ean"), "Daily": item.get("Daily"), "Monthly": item.get("Monthly")} for item in items if "Meter" in item
        }
        if len(items) < 1:
            raise NotFoundError("No EAN found in the alerts table with 'Daily' or 'Monthly' consumption data.", error_code="NO_EANS_FOUND")
        self.eans = {item["Ean"] for item in items if "Ean" in item}
        log_info(f"Eans with alerts activated : {self.eans}")
        return items_dict

    def to_response(self, cursor, params=None):
        data: list = super().to_response(cursor, params)
        return self.process_data_response(data)

    def process_data_response(self, data):
        daily_consumption, monthly_consumption = self.group_consumption(data)
        self.check_consumption(daily_consumption, "daily")
        if self.monthly:
            self.check_consumption(monthly_consumption, "monthly")

        return {"statusCode": 204, "body": None}

    def group_consumption(self, data):
        data_grouped = defaultdict(list)

        for entry in data:
            key = (entry["meterid"], entry["ean"], entry["type_energie"])
            data_grouped[key].append(entry)

        daily_consumption = {}
        monthly_consumption = {}

        for key, records in data_grouped.items():
            records.sort(key=lambda x: x["measuredatetime"], reverse=True)

            # Aggregate daily consumption for records with the same `measuredatetime`
            daily_sum = defaultdict(float)
            for record in records:
                daily_sum[record["measuredatetime"]] += record["measurevalue"]

            daily_values = list(daily_sum.values())
            if len(daily_values) >= 2:
                previous_value = daily_values[1]
                current_value = daily_values[0]
                daily_consumption[key] = current_value - previous_value

            if self.monthly:
                # Aggregate monthly consumption for records with the same `measuredatetime`
                monthly_values = list(daily_sum.values())
                if len(monthly_values) >= 3:
                    monthly_previous_value = monthly_values[2]
                    monthly_consumption[key] = monthly_values[0] - monthly_previous_value

        return daily_consumption, monthly_consumption

    def check_consumption(self, consumption, recurrence):
        for key, conso_value in consumption.items():
            if conso_value > 0:
                num_cpt = key[0]
                evaluated_ean = key[1]
                energy = key[2]
                if recurrence == "daily":
                    limit = float(self.items[num_cpt]["Daily"])
                elif recurrence == "monthly":
                    limit = float(self.items[num_cpt]["Monthly"])
                log_info(f"Ean evaluated : {evaluated_ean} for {recurrence} on : is {conso_value} > {limit}")
                if conso_value > limit:
                    uid_value = self.items[num_cpt]["Uid"]
                    user_info = self.get_contact_by_uid(uid_value)

                    first_name = user_info["firstname"]
                    language = user_info["preferences"].get("Langue", "FR")
                    ean_info = next((ean_data for ean_data in user_info["ean"] if ean_data["ean"] == evaluated_ean), None)
                    address_hash = ean_info["address_hash"]
                    ean_address = ean_info.get("address", {})
                    address = {
                        "Cdpostal": ean_address.get("CdPostal"),
                        "CodePays": "BE",
                        "Localite": ean_address.get("Localite"),
                        "NumRue": ean_address.get("NumRue"),
                        "Rue": ean_address.get("Rue"),
                    }

                    self.register_alert(
                        alert=HistoricAlert.recursive_construct(
                            address_hash=address_hash,
                            address=address,
                            uid=uid_value,
                            ean=evaluated_ean,
                            meter_id=num_cpt,
                            conso_value=conso_value,
                            limit=limit,
                            alert_type=recurrence,
                            date=self.current_date.isoformat(),
                            check_date=self.start_date.isoformat(),
                            energy_type="ELEC" if energy == "elec" else "GAZ",
                            overhead=conso_value - limit,
                        ),
                        ttl=self.current_date + relativedelta(months=6),
                    )
                    self.send_mail_or_sms(user_info.get("email"), evaluated_ean, num_cpt, conso_value, limit, address, first_name, language, energy)

    def apply_hana_env(self, sql_statement):
        end_statement = "'" + self.end_date.strftime("%Y-%m-%d 23:59:00 %z") + " '"
        start_date = "'" + self.start_date.strftime("%Y-%m-%d 00:00:00 %z") + " '"
        if self.monthly:
            end_statement += "OR measureDateTime = '" + self.start_date_monthly.strftime("%Y-%m-%d 00:00:00 %z") + " '"
        sql_statement = sql_statement.format(
            red_table_elec=self.RED_TABLE_ELEC,
            red_table_gaz=self.RED_TABLE_GAZ,
            Ean="(" + ",".join([f"'{ean}'" for ean in self.eans]) + ")",
            StartDate=start_date,
            EndStatement=end_statement,
        )
        return sql_statement

    def get_contact_by_uid(self, uid_value):
        response = self.DYNAMO_USER_TABLE.get_item(Key={"uid": uid_value})
        item = response.get("Item")
        return item

    def register_alert(self, alert: HistoricAlert, ttl: datetime):
        item = alert.model_dump_dynamodb()

        item["expirationTime"] = int(ttl.timestamp())
        self.DYNAMO_HISTORIC_ALERT_TABLE.put_item(Item=item)

    def send_mail_or_sms(self, email, ean, serial_num, conso, limit, adresse, first_name, lang, energy):
        now_time = self.start_date.strftime("%d-%m-%Y")
        energy_sms = "en électricité" if energy == "elec" else "de gaz"
        measure_unit = "kWh" if energy == "elec" else "m³"
        energy_wording = "électricité" if energy == "elec" else energy
        if lang != "FR":
            energy_wording = "Strom" if energy == "elec" else "Gas"

        mail_data = {
            "MEASURE_UNIT": measure_unit,
            "EAN": ean,
            "SERIAL_NUMBER": serial_num,
            "ENERGY_TYPE": energy_wording.capitalize(),
            "CONCERN_TIME": now_time,
            "THRESHOLD": f"{limit:.2f}".replace(".", ","),
            "AMOUNT": f"{conso:.2f}".replace(".", ","),
        }
        api_caller(
            method="post",
            path="/envMessage",
            body={
                "Langue": lang,
                "Header": {
                    "ENERGY_SMS": energy_sms,
                    "TEMPLATE_ID": self.TEMPLATE_NAME,
                    "EMAIL": email,
                    "STREET": f"{adresse['Rue']} {adresse['NumRue']}",
                    "POST_CODE": adresse["Cdpostal"],
                    "CITY": adresse["Localite"],
                    "FIRST_NAME": first_name,
                },
                "Content": mail_data,
            },
            headers=basic_auth_headers(environ["BASICAUTH_AWS"]),
            internal_url=False,
        )
