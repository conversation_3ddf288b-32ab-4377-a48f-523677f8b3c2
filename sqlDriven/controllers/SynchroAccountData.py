from controllers.parallel_controller import <PERSON>llelController
from controllers.ws14 import ws14

from utils.auth_utils import loadUserByUID


class SynchroAccountData(ws14):
    def __init__(self, event, linking):
        super(ParallelController, self).__init__(event, linking)
        self.master_request = None
        uid = event["uid"]
        self.user = loadUserByUID(uid)
        self.login_mail = self.user["email"]

    def layer(self, data, nbItems):
        if not data["user_data"]:
            return None
        ret = self.transform_data_for_dynamo(data)
        self.updateInDynamo(ret, update_date_key="last_daily_sync")
        return ret
