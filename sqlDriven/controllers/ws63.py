from dataclasses import dataclass
from math import ceil

from dataclasses_json import dataclass_json, LetterCase

from controllers.NoPagination import NoPagination
from utils.dict_utils import get
from utils.errors import BadRequestError
from utils.type_utils import int_format, float_format


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class CptTarif:
    id: str
    label: str
    price: float


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class CptForfait:
    name: str
    phase: int
    amperage: float
    power: float
    tarif: CptTarif


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class CptSpec:
    id_cpt: int
    nb_phase: int
    excl_nuit: int
    amperage: float
    puissance: float


class ws63(NoPagination):
    def __init__(self, event, linking):
        super().__init__(event, linking)
        self.cpt_specs: list[CptSpec] = []

    def validate_request_params(self):
        body = self.event["body"]
        body_list = body["Liste"]
        param_names = ["IdCpt", "NbPhase", "ExclNuit", "Amperage", "Puissance"]
        if not all([param in elem for elem in body_list for param in param_names]):
            raise BadRequestError("Chaque élément fourni dans la requête doit contenir IdCpt, NbPhase, ExclNuit, Amperage, Puissance")
        # I am not using generic validators in controller as the parameters are nested in an inner object
        check_float_types = [
            float_format(
                elem[param],
                err=BadRequestError("Le paramètre" + param + "doit être un nombre réel"),
            )
            for elem in body_list
            for param in elem
            if param in ["Amperage", "Puissance"]
        ]
        check_int_types = [
            int_format(
                elem[param],
                err=BadRequestError("Le paramètre" + param + "doit être un nombre entier"),
            )
            for elem in body_list
            for param in elem
            if param in ["IdCpt", "NbPhase"]
        ]

        self.cpt_specs = [CptSpec.from_dict(elem) for elem in body_list]
        return {}

    def to_response(self, data, params=None):
        # Convert all Decimal to float
        tarifs = {elem["Id"]: CptTarif.from_dict(elem) for elem in super().to_response(data)}

        result = [_get_racc_price(tarifs, elem) for elem in self.cpt_specs]

        # *100 each price then /100 total to avoid sum imprecision on float
        total_amount = sum(round(get(elem, "PrixHtva", 0) * 100) for elem in result) / 100
        return {"MontantTotal": total_amount, **{"Liste": result}}


def _get_racc_price(tarifs: dict[str, CptTarif], cpt_spec: CptSpec) -> dict:
    # Le prix d'un nouveau raccordement est donc constitué de :
    # 1 Un forfait branchement : EB102, EB120 ou EB121 (en fonction de la tension)
    # 2 Un forfait puissance : EB103 à EB110 (si autre que tarif ESSENTIEL)
    # 2bis Optionnel, demande custom EK001 * (kva_demande - 69.3 => arrondi au 1/10)
    # 3 Un forfait comptage : EB100 (<69,3 kVA) / EB101 (>= 69.3 kVA) => applicable au EB110 (PRO 69)
    # 4 Un abattement pour fourniture du câble par le client : EB111
    total_price = 0

    # ToDo grab data from SAP when available
    tarif_null = CptTarif(id=None, label="", price=0)
    forfait_choice = [
        CptForfait(phase=1, amperage=40, power=9.2, tarif=tarif_null, name="Essentiel"),
        CptForfait(phase=1, amperage=63, power=14.5, tarif=tarifs.get("EB103"), name="Confort"),
        CptForfait(phase=3, amperage=25, power=10, tarif=tarif_null, name="Essentiel"),
        CptForfait(phase=3, amperage=32, power=12.7, tarif=tarifs.get("EB103"), name="Confort"),
        CptForfait(phase=3, amperage=40, power=15.9, tarif=tarifs.get("EB104"), name="Confort +"),
        CptForfait(phase=3, amperage=50, power=19.9, tarif=tarifs.get("EB105"), name="Power"),
        CptForfait(phase=3, amperage=63, power=25.1, tarif=tarifs.get("EB106"), name="Power +"),
        CptForfait(phase=4, amperage=16, power=11.1, tarif=tarif_null, name="Essentiel"),
        CptForfait(phase=4, amperage=20, power=13.9, tarif=tarifs.get("EB103"), name="Confort"),
        CptForfait(phase=4, amperage=25, power=17.3, tarif=tarifs.get("EB104"), name="Confort +"),
        CptForfait(phase=4, amperage=32, power=22.2, tarif=tarifs.get("EB105"), name="Power"),
        CptForfait(phase=4, amperage=40, power=27.7, tarif=tarifs.get("EB106"), name="Power +"),
        CptForfait(phase=4, amperage=50, power=34.6, tarif=tarifs.get("EB107"), name="PRO 35"),
        CptForfait(phase=4, amperage=63, power=43.6, tarif=tarifs.get("EB108"), name="PRO 44"),
        # CptForfait(phase=4, amperage=66, power=45.7, tarif=tarifs.get("EB108"), name="PRO 44"),
        # CptForfait(phase=4, amperage=69, power=47.8, tarif=tarifs.get("EB108"), name="PRO 44"),
        # CptForfait(phase=4, amperage=72, power=49.9, tarif=tarifs.get("EB108"), name="PRO 44"),
        # CptForfait(phase=4, amperage=75, power=52, tarif=tarifs.get("EB108"), name="PRO 44"),
        CptForfait(phase=4, amperage=80, power=55.4, tarif=tarifs.get("EB109"), name="PRO 55"),
        CptForfait(phase=4, amperage=100, power=69.3, tarif=tarifs.get("EB110"), name="PRO 69"),
    ]

    # STEP 1 Un forfait branchement : EB102, EB120 ou EB121 (en fonction de la tension)
    # - EB102 | Fft Racc BT - Branchement monophasé
    # - EB120 | Fft Racc BT - Bcht tétraphasé 3N400 V
    # - EB121 | Fft Racc BT - Bcht triphasé 3X230 V
    max_forfait = forfait_choice[-1]

    if cpt_spec.nb_phase == 1:
        total_price += tarifs["EB102"].price
    elif cpt_spec.nb_phase == 3:
        total_price += tarifs["EB120"].price
    elif cpt_spec.nb_phase == 4:
        total_price += tarifs["EB121"].price

    # STEP 2 Un forfait puissance : EB103 à EB110 (si autre que tarif ESSENTIEL)
    selected_forfait = None
    if cpt_spec.puissance < max_forfait.power:
        for forfait in forfait_choice:
            if cpt_spec.nb_phase == forfait.phase and cpt_spec.amperage <= forfait.amperage and cpt_spec.puissance <= forfait.power:
                selected_forfait = forfait
                break
        if selected_forfait:
            total_price += selected_forfait.tarif.price
        else:
            # If no forfait found that match
            return {"IdCpt": cpt_spec.id_cpt}
    else:
        selected_forfait = max_forfait
        total_price += selected_forfait.tarif.price

        # STEP 2bis Demande custom EK001 * (kva_demande - 69.3 => arrondi au 1/10)
        # - EK001 | BT : Redevance kVA Prélèvement
        total_price += tarifs["EK001"].price * ceil(cpt_spec.puissance * 10 - max_forfait.power * 10) / 10

    # STEP 3 Un forfait comptage : EB100 (<69,3 kVA) / EB101 (>= 69.3 kVA) => applicable au EB110 (PRO 69)
    if selected_forfait == max_forfait:
        total_price += tarifs["EB101"].price
    else:
        total_price += tarifs["EB100"].price

    # STEP 4 Un abattement pour fourniture du câble par le client : EB111
    # - EB111 | Abatt. pour racc sans fourn + pose GRD
    # ToDo

    return {
        "IdCpt": cpt_spec.id_cpt,
        "PrixHtva": round(total_price, 2),
        "LibIdCpt": selected_forfait.name,
    }
