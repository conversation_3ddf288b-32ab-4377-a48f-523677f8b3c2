#!/usr/bin/env python3
import contextlib
import json
import os

from controllers.Controller import Controller

import utils.resa_api_constants as constants
from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import load_s3_file
from utils.DecimalEncoder import DecimalEncoder
from utils.dict_utils import get
from utils.errors import BadRequestError, HttpError
from utils.log_utils import LogTime


def import_controller(elt: str) -> type[Controller]:
    mod = __import__("controllers." + elt)
    mod = getattr(mod, elt)
    return getattr(mod, elt)


def run_main_query(instance: Controller) -> dict:
    try:
        request_params = {
            "Langue": get(os.environ, "LANG", "FR", default_on_empty=True),
            **instance.validate_request_params(),
        }
    except ValueError as e:
        raise BadRequestError(str(e), headers=instance.headers()) from e
    sql_statement = instance.load_sql_file(instance.sql_file())
    sql_statement = instance.apply_hana_env(sql_statement)

    try:
        try:
            with LogTime("Open connection"):
                cursor = instance.db_connection()
        except Exception as e:
            raise HttpError(500, constants.errmsg_dict["connection"], instance.headers()) from e
        with LogTime("Execute query"):
            cursor = instance.execute_query(sql_statement, request_params)
        return instance.to_response(cursor, request_params)
    finally:
        with contextlib.suppress(Exception):
            cursor.close()


@aws_lambda_handler
def handler(event, context):
    path_data = {}
    is_eventbridge = False
    original_event = event.get("original_event", None)
    if original_event:
        is_eventbridge = original_event.get("detail-type") == "Scheduled Event"

    with contextlib.suppress(ValueError, TypeError):
        event["body"] = json.loads(event.get("body", "{}") or "{}")
    if not is_eventbridge:
        method = event["httpMethod"]
        path = event["resource"]
        mapping_s3 = load_s3_file(os.environ["MAPPING_BUCKET_NAME"], os.environ["MAPPING_BUCKET_FILE"])  # pylint: disable=no-member
        mapping = json.loads(mapping_s3)
        path_data = mapping[path][method.upper()]
    else:
        # TODO : move this in linking.json for future usage
        task = event.get("task")
        if task == "check_alert":
            path_data = {"type": "sqlDriven", "secret": "{REDSHIFT_SECRET}", "controller": "AlerteConso", "sql_template": "sqlStatements/alerte_conso.sql"}
        elif task == "check_bilan":
            path_data = {"type": "sqlDriven", "secret": "{REDSHIFT_SECRET}", "controller": "BilanConso", "sql_template": "sqlStatements/bilan_conso.sql"}
        elif task == "synchro_account":
            path_data = {
                "type": "sqlDriven",
                "controller": "TriggerSynchroAccount",
            }

            controller_name = path_data.get("controller", "Controller")
            controller = import_controller(controller_name)
            instance = controller(event, path_data)
            # noinspection PyUnresolvedReferences
            instance.process()
            return {
                "statusCode": 204,
            }
    controller_name = path_data.get("controller", "Controller")
    controller = import_controller(controller_name)
    instance = controller(event, path_data)
    data = run_main_query(instance)
    return {
        "isBase64Encoded": instance.base64,
        "statusCode": 200,
        "headers": instance.headers(),
        "body": data if instance.base64 or instance.raw_response else json.dumps(data, cls=DecimalEncoder),
    }


if __name__ == "__main__":
    print(
        json.dumps(
            handler(
                {
                    "task": "synchro_account",
                    "original_event": {
                        "version": "0",
                        "id": "93b72708-c6b3-05bb-a458-680501151c40",
                        "detail-type": "Scheduled Event",
                        "source": "aws.events",
                        "account": "************",
                        "time": "2025-06-29T07:00:00Z",
                        "region": "eu-west-1",
                        "resources": ["arn:aws:events:eu-west-1:************:rule/every-day-at-8-qta"],
                        "detail": {},
                    },
                },
                None,
            ),
        ),
    )
    """
    print(json.dumps(handler(
        {'resource': '/ep',
         'method': 'get',
         'queryStringParameters': {'Lat0': 50.0, 'Long0': 5.665, 'Lat1': 55.0, 'Long1': 6.0, 'PageSize': 100},
         'httpMethod': 'GET'}, None)))
     """
    """
     print(json.dumps(handler(
        {'resource': '/pannes/{Id}',
         'method': 'get',
         'pathParameters': {'id' : '************'},
         'httpMethod': 'GET'}, None)))
    
    """
"""
    541456700003233803
    541456700000048189
    541460900002617502 -> Gaz
print(
        json.dumps(
            handler(
                {   'headers' : {},
                    'body': {},
                    'resource': '/ean/{Ean}',
                    'method': 'get',
                    'queryStringParameters': {},
                    'pathParameters': {
                        'Ean': 541460900002617502
                    },
                    'httpMethod': 'GET'
                }, None)))
"""
