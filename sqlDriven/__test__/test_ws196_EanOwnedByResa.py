import unittest
from unittest.mock import Mock, patch

from utils.api import api_caller
from utils.mocking import mock_api


def _fetch_mock_data(cursor, query):
    if query["Ean"] == "541449011000007794":
        return {
            "ResaIsGRD": True,
        }
    else:
        return {
            "ResaIsGRD": False,
        }


@mock_api
@patch(
    "controllers.Controller.Controller.to_response",
    new=Mock(side_effect=_fetch_mock_data),
)
class TestWS196EanOwnedByResa(unittest.TestCase):
    def test_ean_owned_by_resa(self):
        valid_ean = "541449011000007794"
        response = api_caller("GET", f"/ean/{valid_ean}/grd", raw=True)
        # Supposons que la réponse attendue est {"ResaIsGRD": True}
        self.assertTrue(response.json()["ResaIsGRD"], True)

    def test_ean_not_owned_by_resa(self):
        invalid_ean = "541449020714489383"
        response = api_caller("GET", f"/ean/{invalid_ean}/grd", raw=True)
        # Supposons que la réponse attendue est {"ResaIsGRD": False}
        self.assertFalse(response.json()["ResaIsGRD"], False)
