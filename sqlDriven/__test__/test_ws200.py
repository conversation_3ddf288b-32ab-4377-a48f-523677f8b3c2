import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, patch, Mock

from controllers.ws200 import WS200Input
from controllers.ws200 import ws200
from utils.api import api_caller
from utils.errors import UnauthorizedError, BadRequestError
from utils.mocking import mock_api
from utils.mocking import mock_environnement


class TestWS200Input(unittest.TestCase):
    @patch("controllers.ws200.User")
    def test_from_event(self, mock_user_class):
        event = {"queryStringParameters": {"ean": "1234567890123"}}
        mock_user_class.from_event.side_effect = UnauthorizedError()
        ws200_input = WS200Input.from_event(event)
        self.assertEqual(ws200_input.ean, event["queryStringParameters"]["ean"])
        self.assertIsNone(ws200_input.user)

    @patch("controllers.ws200.User")
    def test_from_event_with_user(self, mock_user_class):
        event = {"queryStringParameters": {"ean": "1234567890123"}}
        user_instance = mock_user_class.from_event.return_value
        ws200_input = WS200Input.from_event(event)
        self.assertEqual(ws200_input.ean, event["queryStringParameters"]["ean"])
        self.assertEqual(ws200_input.user, user_instance)


class TestWS200(unittest.TestCase):
    def setUp(self):
        self.controller = ws200(None, None)

    @patch("controllers.ws200.NoPagination.to_response")
    def test_execute_query_valid(self, mock_super_to_response):
        # Give a response from execute_query in Controller
        mock_super_to_response.return_value = [
            {
                "Address_Street": "Test Street",
                "Address_Number": "Test Number",
                "Address_Poscode": "Test Poscode",
                "Address_City": "Test City",
                "Address_Box": "Test Box",
                "Meter_Type": "Test Type",
                "Meter_Number": "Test Number",
                "Meter_Power": "Test Power",
                "Meter_Amper": "Test Amper",
                "Meter_Tarif": "Test Tarif",
                "Meter_Production": "Test Production",
                "Meter_Smart": "Test Smart",
                "Ean": "Test Ean",
            }
        ]
        # Now with mocking in place run execute_query function
        self.controller.input = MagicMock()
        self.controller.input.user = True
        result = self.controller.to_response(None)
        assert len(result) == 1
        assert result[0]["Address"]["Street"] == "Test Street"

    @patch("controllers.ws200.NoPagination.to_response")
    def test_execute_query_raises_error_for_non_matching_meter_id(self, mock_super_to_response):
        # Give a response from execute_query in Controller
        mock_super_to_response.return_value = [
            {
                "Address_Street": "Test Street",
                "Address_Number": "Test Number",
                "Address_Poscode": "Test Poscode",
                "Address_City": "Test City",
                "Address_Box": "Test Box",
                "Meter_Type": "Test Type",
                "Meter_Number": "Another Meter Number",
                "Meter_Power": "Test Power",
                "Meter_Amper": "Test Amper",
                "Meter_Tarif": "Test Tarif",
                "Meter_Production": "Test Production",
                "Meter_Smart": "Test Smart",
                "Ean": "Test Ean",
            }
        ]
        # Now with mocking in place run execute_query function
        self.controller.input = MagicMock()
        self.controller.input.user = False
        self.controller.input.meter_id = "Test Meter Id"
        with self.assertRaises(BadRequestError):
            self.controller.to_response(None)

    @patch("controllers.ws200.WS200Input.from_event")
    @patch("controllers.ws200.NoPagination.__init__")
    def test_ws200_init(self, mock_super, mock_from_event):
        event = MagicMock()
        linking = MagicMock()

        ws200_obj = ws200(event, linking)

        mock_super.assert_called_once_with(event, linking)
        mock_from_event.assert_called_once_with(event)
        self.assertEqual(ws200_obj.input, mock_from_event.return_value)

    @mock_environnement
    def test_ws200_params(self):
        ws200({"queryStringParameters": {"Ean": "1234567890123", "MeterId": "123"}}, None).validate_request_params()

    @mock_environnement
    def test_ws200_missing_params(self):
        with self.assertRaises(BadRequestError) as error:
            ws200({"queryStringParameters": {"Ean": "1234567890123"}}, None).validate_request_params()
        self.assertEqual(error.exception.error_code, "MISSING_PARAMETERS")
        with self.assertRaises(BadRequestError) as error:
            ws200({"queryStringParameters": {"MeterId": "123"}}, None).validate_request_params()
        self.assertEqual(error.exception.error_code, "MISSING_PARAMETERS")

    def test_validate_request_params_missing_parameters(self):
        ws200_obj = ws200(MagicMock(), MagicMock())
        ws200_obj.input = MagicMock()
        ws200_obj.input.user = None
        ws200_obj.input.ean = None
        ws200_obj.input.meter_id = None

        with self.assertRaises(BadRequestError) as ex:
            ws200_obj.validate_request_params()
        self.assertEqual(ex.exception.error_code, "MISSING_PARAMETERS")

    @patch("controllers.ws200.NoPagination.to_response")
    def test_to_response_bad_request(self, mock_super):
        ws200_obj = ws200(MagicMock(), MagicMock())
        ws200_obj.input = MagicMock()
        ws200_obj.input.user = None
        ws200_obj.input.meter_id = "123"

        cursor = MagicMock()
        db_result = [{"Meter_Number": "456"}]
        mock_super.return_value = db_result

        with self.assertRaises(BadRequestError) as ex:
            ws200_obj.to_response(cursor)
        self.assertEqual(ex.exception.error_code, "INCORRECT_PARAMETERS")


@patch(
    "controllers.ws200.NoPagination.to_response",
    new=Mock(
        return_value=[
            {
                "Address_Street": "Test Street",
                "Address_Number": "Test Number",
                "Address_Poscode": "Test Poscode",
                "Address_City": "Test City",
                "Address_Box": "Test Box",
                "Meter_Type": "Test Type",
                "Meter_Number": "Test Number 1",
                "Meter_Power": "Test Power",
                "Meter_Amper": "Test Amper",
                "Meter_Tarif": "Test Tarif",
                "Meter_Production": "Test Production",
                "Meter_Smart": "Test Smart",
                "Ean": "Test Ean 1",
            },
            {
                "Address_Street": "Test Street",
                "Address_Number": "Test Number",
                "Address_Poscode": "Test Poscode",
                "Address_City": "Test City",
                "Address_Box": "Test Box",
                "Meter_Type": "Test Type",
                "Meter_Number": "Test Number 2",
                "Meter_Power": "Test Power",
                "Meter_Amper": "Test Amper",
                "Meter_Tarif": "Test Tarif",
                "Meter_Production": "Test Production",
                "Meter_Smart": "Test Smart",
                "Ean": "Test Ean 2",
            },
            {
                "Address_Street": "Test Street",
                "Address_Number": "Test Number",
                "Address_Poscode": "Test Poscode",
                "Address_City": "Test City",
                "Address_Box": "Test Box",
                "Meter_Type": "Test Type",
                "Meter_Number": "Test Number 3",
                "Meter_Power": "Test Power",
                "Meter_Amper": "Test Amper",
                "Meter_Tarif": "Test Tarif",
                "Meter_Production": "Test Production",
                "Meter_Smart": "Test Smart",
                "Ean": "Test Ean 2",
            },
        ]
    ),
)
@patch("controllers.ws200.User.from_event", new=Mock(return_value=MagicMock(ean_ids=["Test Ean 1", "Test Ean 2"])))
@mock_api
class TestWS200Call(unittest.TestCase):
    def test_something(self):
        response = api_caller("GET", "/ean/meter", raw=True)
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(len(response_data), 1)
        self.assertEqual(response_data[0]["Address"]["Street"], "Test Street")
        self.assertEqual(len(response_data[0]["Eans"]), 2)
        self.assertEqual(response_data[0]["Eans"][0]["Ean"], "Test Ean 1")
        self.assertEqual(len(response_data[0]["Eans"][0]["Meters"]), 1)
        self.assertEqual(response_data[0]["Eans"][0]["Meters"][0]["Number"], "Test Number 1")
        self.assertEqual(response_data[0]["Eans"][1]["Ean"], "Test Ean 2")
        self.assertEqual(len(response_data[0]["Eans"][1]["Meters"]), 2)
        self.assertEqual(response_data[0]["Eans"][1]["Meters"][0]["Number"], "Test Number 2")
        self.assertEqual(response_data[0]["Eans"][1]["Meters"][1]["Number"], "Test Number 3")


if __name__ == "__main__":
    unittest.main()
