import unittest
from decimal import Decimal
from unittest.mock import Mock, patch

from utils.api import api_caller
from utils.mocking import mock_api


@patch(
    "controllers.Controller.Controller.to_response",
    new=Mock(
        return_value=[
            {
                "Id": "EB100",
                "Label": "Fft Racc BT - Comptage",
                "Price": Decimal("243.22"),
            },
            {
                "Id": "EB101",
                "Label": "Fft Racc BT–Comptage PRO 69,3 à 83,2 kVA",
                "Price": Decimal("3088.54"),
            },
            {
                "Id": "EB102",
                "Label": "Fft Racc BT - Branchement",
                "Price": Decimal("728.76"),
            },
            {
                "Id": "EB103",
                "Label": "Fft Confort - Puiss. màd",
                "Price": Decimal("883.8"),
            },
            {
                "Id": "EB104",
                "Label": "Fft Confort Plus - Puiss. màd",
                "Price": Decimal("1595.15"),
            },
            {
                "Id": "EB105",
                "Label": "Fft Power - Puiss. màd",
                "Price": Decimal("4537.56"),
            },
            {
                "Id": "EB106",
                "Label": "Fft Power Plus - Puiss. màd",
                "Price": Decimal("5690.81"),
            },
            {
                "Id": "EB107",
                "Label": "Fft PRO 35 - Puiss. màd",
                "Price": Decimal("7178.18"),
            },
            {
                "Id": "EB108",
                "Label": "Fft PRO 44 - Puiss. màd",
                "Price": Decimal("9118.23"),
            },
            {
                "Id": "EB109",
                "Label": "Fft PRO 55 - Puiss. màd",
                "Price": Decimal("11661.85"),
            },
            {
                "Id": "EB110",
                "Label": "Fft PRO 69 - Puiss. màd",
                "Price": Decimal("14658.15"),
            },
            {
                "Id": "EB111",
                "Label": "Abatt. pour racc sans fourn + pose GRD",
                "Price": Decimal("-357.27"),
            },
            {
                "Id": "EK001",
                "Label": "BT : Redevance kVA Prélèvement",
                "Price": Decimal("215.56"),
            },
            {
                "Id": "EB121",
                "Label": "Fft Racc BT - Bcht triphasé 3X230 V",
                "Price": Decimal("728.76"),
            },
            {
                "Id": "EB120",
                "Label": "Fft Racc BT - Bcht tétraphasé 3N400 V",
                "Price": Decimal("728.76"),
            },
        ]
    ),
)
@mock_api
class TestWS63(unittest.TestCase):
    test_body = {
        "Liste": [
            {
                "IdCpt": 1,
                "NbPhase": -1,
                "ExclNuit": 0,
                "Amperage": 10,
                "Puissance": 5.5,
            },
            {"IdCpt": 2, "NbPhase": 1, "ExclNuit": 0, "Amperage": 10, "Puissance": 5.5},
            {
                "IdCpt": 3,
                "NbPhase": 1,
                "ExclNuit": 0,
                "Amperage": 50,
                "Puissance": 11.5,
            },
            {
                "IdCpt": 4,
                "NbPhase": 3,
                "ExclNuit": 0,
                "Amperage": 40,
                "Puissance": 15.9,
            },
            {
                "IdCpt": 5,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 30,
                "Puissance": 10.7,
            },
            {
                "IdCpt": 6,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 33,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 7,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 45,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 8,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 51,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 9,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 63,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 10,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 80,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 11,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 100,
                "Puissance": 22.7,
            },
            {
                "IdCpt": 12,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 100,
                "Puissance": 69.3,
            },
            {
                "IdCpt": 13,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 100,
                "Puissance": 69.4,
            },
            {
                "IdCpt": 14,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 100,
                "Puissance": 100,
            },
            {"IdCpt": 15, "NbPhase": 3, "ExclNuit": 0, "Amperage": 25, "Puissance": 10},
            {
                "IdCpt": 16,
                "NbPhase": 4,
                "ExclNuit": 0,
                "Amperage": 16,
                "Puissance": 11.1,
            },
        ]
    }
    test_response = {
        "MontantTotal": 141016.64,
        "Liste": [
            {"IdCpt": 1},
            {"IdCpt": 2, "PrixHtva": 971.98, "LibIdCpt": "Essentiel"},
            {"IdCpt": 3, "PrixHtva": 1855.78, "LibIdCpt": "Confort"},
            {"IdCpt": 4, "PrixHtva": 2567.13, "LibIdCpt": "Confort +"},
            {"IdCpt": 5, "PrixHtva": 5509.54, "LibIdCpt": "Power"},
            {"IdCpt": 6, "PrixHtva": 6662.79, "LibIdCpt": "Power +"},
            {"IdCpt": 7, "PrixHtva": 8150.16, "LibIdCpt": "PRO 35"},
            {"IdCpt": 8, "PrixHtva": 10090.21, "LibIdCpt": "PRO 44"},
            {"IdCpt": 9, "PrixHtva": 10090.21, "LibIdCpt": "PRO 44"},
            {"IdCpt": 10, "PrixHtva": 12633.83, "LibIdCpt": "PRO 55"},
            {"IdCpt": 11, "PrixHtva": 18475.45, "LibIdCpt": "PRO 69"},
            {"IdCpt": 12, "PrixHtva": 18475.45, "LibIdCpt": "PRO 69"},
            {"IdCpt": 13, "PrixHtva": 18497.01, "LibIdCpt": "PRO 69"},
            {"IdCpt": 14, "PrixHtva": 25093.14, "LibIdCpt": "PRO 69"},
            {"IdCpt": 15, "PrixHtva": 971.98, "LibIdCpt": "Essentiel"},
            {"IdCpt": 16, "PrixHtva": 971.98, "LibIdCpt": "Essentiel"},
        ],
    }

    def test_success(self):
        response = api_caller("POST", "/tarifs", raw=True, body=self.test_body)
        self.assertEqual(response.status_code, 200)
        self.assertDictEqual(response.json(), self.test_response)


if __name__ == "__main__":
    unittest.main()
