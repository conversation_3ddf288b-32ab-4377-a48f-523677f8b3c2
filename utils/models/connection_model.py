from dataclasses import dataclass
from os import environ
from typing import List, Optional

from dataclasses_json import dataclass_json, LetterCase

from utils.models.address import Address


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Company:
    """
    A class to represent a company.

    Attributes
    ----------
    name : str
        The name of the company.
    legal_status : str
        The legal status of the company.
    tva : str
        The TVA (Taxe sur la Valeur Ajoutée) number of the company.
    """

    name: str
    legal_status: str
    tva: str


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Meter:
    """
    A class to represent a meter object used to track electricity consumption.

    Attributes
    ----------
    ean : str
        The EAN (European Article Number) of the meter.
    number : str
        The unique number of the meter.
    photo : Optional[str]
        The optional filepath of the meter's photo, by default None.
    """

    ean: str
    number: str
    photo: Optional[str] = None


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Connection:
    """
    A class to represent a connection request.

    Attributes
    ----------
    energy_type : str
        The type of energy (e.g., 'ELEC', 'GAZ').
    work_type : str
        The type of work.
    applicant_type : str
        The type of applicant.
    act_as : str
        The role of the applicant.
    name : str
        The name of the applicant.
    firstname : str
        The first name of the applicant.
    email : str
        The email address of the applicant.
    phone : str
        The phone number of the applicant.
    address : Address
        The address details of the connection.
    meters : List[Meter]
        The list of meters associated with the connection.
    contact : Optional[Address]
        The contact address of the applicant (optional).
    billing : Optional[Address]
        The billing address (optional).
    company : Optional[Company]
        The company details (optional).

    Methods
    -------
    sect_activite()
        Returns sector activity code based on the energy type.
    to_sap_format_ws35()
        Converts the object's attributes into the SAP format for WS35.
    """

    energy_type: str
    work_type: str
    applicant_type: str
    act_as: str
    name: str
    firstname: str
    email: str
    phone: str
    address: Address
    meters: List[Meter]
    contact: Optional[Address] = None
    billing: Optional[Address] = None
    company: Optional[Company] = None

    @property
    def sect_activite(self):
        """
        Returns sector activity code based on the energy type.

        Returns
        -------
        str
            '01' if the energy type is 'ELEC'
            '02' if the energy type is 'GAZ'
            None otherwise.
        """
        if self.energy_type == "ELEC":
            return "01"
        elif self.energy_type == "GAZ":
            return "02"
        else:
            return None

    def to_sap_format_ws35(self):
        """
        Converts the object's attributes into SAP format for WS35.

        Returns
        -------
        dict
            the data in SAP format.
        """
        language = environ["LANG"]
        return {
            "Langue": language,
            "Partenaire": [
                {
                    "TypePartenaire": "DEMANDEUR",
                    "IdPartenaire": "",  # Num BP, could maybe be useful if connected user
                    "Nom": self.name,
                    "Prenom": self.firstname,
                    "CodeCivilite": "9999",  # To review is it hard coded ?
                    "Langue": language,
                    "Gsm": self.phone,
                    "Tel": "",
                    "Email": self.email,
                    "Fonction": "",
                    "NumTVA": self.company.tva if self.company else "",
                    "AssujetiTVA": "Y" if self.company else "N",
                    "TypeURD": self.applicant_type,
                    "NomEntreprise": self.company.name if self.company else "",
                    "FormeJuridique": self.company.legal_status if self.company else "",
                    "Adresse": {
                        "Rue": self.contact.street if self.contact else self.address.street,
                        "NumRue": self.contact.number if self.contact else self.address.number,
                        "NumCmpt": self.contact.box if self.contact else self.address.box,
                        "CdPostal": self.contact.postcode if self.contact else self.address.postcode,
                        "Localite": self.contact.city if self.contact else self.address.city,
                        "Pays": self.contact.country if self.contact else self.address.country,
                    },
                }
            ],
            "Adresse": {
                "Rue": self.address.street,
                "NumRue": self.address.number,
                "NumCmpt": self.address.box,
                "CdPostal": self.address.postcode,
                "Localite": self.address.city,
                "ParcelleSpw": "",
                "Pays": "BE",
                "Emplacement": "",
            },
            "Demande": [
                {
                    "SectActivite": self.sect_activite,
                    "Ean": meter.ean,
                    "TypeTravail": "S20" if self.work_type == "MODI_SMART" else "R02",  # R01 if NOUV_* else R02
                    "Details": [
                        {
                            "CodeDetail": "CD_TYPE",
                            "Valeur": self.work_type,
                        }
                    ],
                    "NbCompteurs": 1,
                    "Compteur": [
                        {
                            "Compteur": 1,
                            "DetailsCompteurs": [
                                {"CodeDetailCpt": "NUMCPT", "ValeurDetailCpt": meter.number},
                            ],
                        }
                    ],
                }
                for meter in self.meters
            ],
            "Complement": [
                {"Libelle": "REMARQUE", "Valeur": self.act_as, "Titre": "N"},
            ],
        }
