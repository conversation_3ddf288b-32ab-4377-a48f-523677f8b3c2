from dataclasses import dataclass, field
from typing import Optional, List

from dataclasses_json import dataclass_json, LetterCase

from utils.models.address import Address


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class Meter:
    """
    Holds the meter details.

    Attributes
    ----------
    type : str
        Type of the meter (For example, "elec" for electrical)
    number : str
        An unique identifier for meter
    tarif : str
        Tariff type ("jour" for daytime)
    smart : bool
        Indicates if the meter is smart or not
    power : Optional[float], default None
        Power rating of the meter (kW)
    amper : Optional[float], default None
        Current capacity (A)
    production : Optional[float], default None
        Production capacity (kVA)
    """

    type: str
    number: str
    tarif: str
    smart: bool
    power: Optional[float] = None
    amper: Optional[float] = None
    production: Optional[float] = None


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class EanMeters:
    """
    Holds the ean details with meters.

    Attributes
    ----------
    ean : str
        An unique identifier
    """

    ean: str
    meters: List[Meter] = field(default_factory=list)


@dataclass_json(letter_case=LetterCase.PASCAL)
@dataclass
class EanAddressInfo:
    """
    Holds the ean, adresse details.

    Attributes
    ----------
    address : Address
        Address of the installation
    eans : List[EanMeters]
        List of ean details with meters
    """

    address: Address
    eans: List[EanMeters]
