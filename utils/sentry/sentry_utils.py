import contextlib
import logging
from os import environ

import sentry_sdk
from sentry_sdk import capture_exception
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

from utils.auth_utils import getUserId


def init_sentry() -> None:
    """
    Initialize Sentry for error tracking and performance monitoring.

    This function configures the Sentry SDK to capture errors, events, and performance data,
    and integrates with AWS Lambda and logging mechanisms in the application.
    Sentry is setup with specific configuration parameters such as environment, release
    info, trace and profile sample rates, as well as enabling stack trace attachment.
    """
    sentry_sdk.init(
        dsn="https://<EMAIL>:3443/4505278531371008",
        integrations=[
            AwsLambdaIntegration(timeout_warning=True),
            LoggingIntegration(
                level=logging.INFO,  # Capture info and above as breadcrumbs
                event_level=None,  # Never send errors logs as events
            ),
        ],
        traces_sample_rate=0.02,
        profiles_sample_rate=1,
        environment=environ.get("STAGE"),
        release=f"MyResaAPI@{environ.get('API_VERSION')}",
        max_request_body_size="always",
        attach_stacktrace=True,
    )


def set_user_in_sentry(event: dict) -> None:
    """
    Set user and context data in Sentry for tracing and monitoring.

    This function attempts to set the user ID in Sentry and provides additional
    context for tracing, using data extracted from the event's headers.
    It gracefully handles any exceptions that occur during this process to
    ensure the application flow remains unaffected.

    Parameters
    ----------
    event : dict
        The event dictionary containing information about the request, typically
        including headers.
        The headers provide the AWS trace ID and a custom web trace ID for context.

    """
    with contextlib.suppress(Exception):
        sentry_sdk.set_user({"id": getUserId(event)})
    sentry_sdk.set_context(
        "application_trace",
        {
            "aws_trace_id": event.get("headers", {}).get("X-Amzn-Trace-Id"),
            "web_resa_trace_id": event.get("headers", {}).get("X-Web-Resa-Trace-Id"),
        },
    )


def capture_exception_in_sentry(e: Exception) -> None:
    """
    Capture and report exceptions to Sentry, avoiding reporting in a local or sandbox environment.

    This function checks the environment variables to determine whether the current
    environment is local or running in a sandbox mode.
    If it is none of those, it sends the provided exception to Sentry for error tracking and monitoring.

    Parameters
    ----------
    e : Exception
        The exception object to be reported to Sentry.

    """
    if environ.get("LOCAL", "false") != "true" and environ.get("STAGE", "")[:8].lower() != "sandbox-":
        capture_exception(e)


def capture_message_in_sentry(msg: str, extra: dict | None = None, level: str | None = None) -> None:
    """
    Capture a message in Sentry with optional context and logging level.

    This function sends a message to Sentry for error monitoring, including optional
    additional details and logging level.
    It only runs if not in a local or sandbox environment, defined by certain environment variables.

    Parameters
    ----------
    msg : str
        The message to capture in Sentry.
    extra : dict, optional
        A dictionary of additional details or context to attach to the message.
    level : str, optional
        The logging level to assign to the message (e.g., "error", "info").

    """
    if environ.get("LOCAL", "false") != "true" and environ.get("STAGE", "")[:8].lower() != "sandbox-":
        with sentry_sdk.new_scope() as scope:
            if extra:
                for k, v in extra.items():
                    scope.set_extra(k, v)
            sentry_sdk.capture_message(msg, level)
